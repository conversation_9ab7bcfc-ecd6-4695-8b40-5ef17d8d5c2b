/**
 * Utility to load the Razorpay SDK
 */

// Define the Razorpay interface
interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description?: string;
  image?: string;
  order_id?: string;
  subscription_id?: string;
  handler: (_response: RazorpayResponse) => void;
  prefill?: {
    name?: string;
    email?: string;
    contact?: string;
  };
  notes?: Record<string, string>;
  theme?: {
    color?: string;
  };
  modal?: {
    ondismiss?: () => void;
    escape?: boolean;
    animation?: boolean;
  };
}

interface RazorpayResponse {
  razorpay_payment_id: string;
  razorpay_order_id?: string;
  razorpay_signature?: string;
  razorpay_subscription_id?: string;
}

interface RazorpayInstance {
  on: (_event: string, _handler: (_response: unknown) => void) => void;
  open: () => void;
  close: () => void;
}

interface RazorpayClass {
  new (_options: RazorpayOptions): RazorpayInstance;
}

declare global {
  interface Window {
    Razorpay: RazorpayClass;
  }
}

/**
 * Load the Razorpay SDK
 * @returns A promise that resolves when the SDK is loaded
 */
export const loadRazorpaySDK = (): Promise<boolean> => {
  return new Promise((resolve) => {
    // Check if Razorpay is already loaded
    if (window.Razorpay) {
      resolve(true);
      return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    script.onload = () => resolve(true);
    script.onerror = () => {
      console.error('[RAZORPAY_ERROR] Failed to load Razorpay SDK');
      resolve(false);
    };

    // Add script to document
    document.body.appendChild(script);
  });
};

/**
 * Initialize Razorpay checkout for a subscription
 * @param options The Razorpay options
 * @returns The Razorpay instance
 */
export const initializeRazorpayCheckout = async (options: RazorpayOptions): Promise<RazorpayInstance | null> => {
  try {
    // Load the Razorpay SDK
    const isLoaded = await loadRazorpaySDK();
    if (!isLoaded) {
      throw new Error('Failed to load Razorpay SDK');
    }

    // Create Razorpay instance
    const razorpay = new window.Razorpay(options);

    return razorpay;
  } catch (error) {
    console.error('[RAZORPAY_ERROR] Error initializing Razorpay checkout:', error);
    return null;
  }
};

/**
 * Open Razorpay checkout for a subscription
 * @param keyId The Razorpay key ID
 * @param subscriptionId The subscription ID
 * @param options Additional options
 * @param timeoutMs Timeout in milliseconds (default: 10 minutes)
 * @returns A promise that resolves when the payment is complete
 */
export const openRazorpaySubscriptionCheckout = async (
  keyId: string,
  subscriptionId: string,
  options: {
    name: string;
    description?: string;
    prefill?: {
      name?: string;
      email?: string;
      contact?: string;
    };
    notes?: Record<string, string>;
    theme?: {
      color?: string;
    };
  },
  timeoutMs: number = 10 * 60 * 1000 // 10 minutes default
): Promise<RazorpayResponse> => {
  return new Promise(async (resolve, reject) => {
    let timeoutId: NodeJS.Timeout | null = null;
    let isResolved = false;

    // Set up timeout
    timeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        const timeoutError = {
          timeout: true,
          message: `Payment timeout after ${timeoutMs / 1000} seconds. Please try again.`
        };
        reject(timeoutError);
      }
    }, timeoutMs);

    const safeResolve = (response: RazorpayResponse) => {
      if (!isResolved) {
        isResolved = true;
        if (timeoutId) clearTimeout(timeoutId);
        resolve(response);
      }
    };

    const safeReject = (error: unknown) => {
      if (!isResolved) {
        isResolved = true;
        if (timeoutId) clearTimeout(timeoutId);
        reject(error);
      }
    };

    try {
      // Initialize Razorpay checkout
      const razorpay = await initializeRazorpayCheckout({
        key: keyId,
        subscription_id: subscriptionId,
        name: options.name,
        description: options.description || 'Subscription Payment',
        prefill: options.prefill,
        notes: options.notes,
        theme: options.theme || { color: '#6366f1' },
        handler: (response) => {
          safeResolve(response);
        },
        modal: {
          ondismiss: () => {
            // Create a special cancellation object instead of an error
            // This will be handled differently in the catch block
            const cancellation = {
              cancelled: true,
              message: 'Payment cancelled by user'
            };
            safeReject(cancellation);
          },
          escape: false,
          animation: true,
        },
        // These are required by Razorpay but not used for subscription checkout
        amount: 0,
        currency: 'INR',
      });

      if (!razorpay) {
        console.error(`[RAZORPAY_ERROR] Failed to initialize Razorpay checkout`);
        safeReject(new Error('Failed to initialize Razorpay checkout'));
        return;
      }

      // Open the checkout
      razorpay.open();
    } catch (error) {
      safeReject(error);
    }
  });
};
